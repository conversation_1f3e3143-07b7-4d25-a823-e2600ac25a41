
output "task_definition_arn" {
  value = aws_ecs_task_definition.td.arn
}

output "efs_file_system_id" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].id : null
  description = "EFS file system ID (if EFS is enabled)"
}

output "efs_file_system_arn" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].arn : null
  description = "EFS file system ARN (if EFS is enabled)"
}

output "efs_dns_name" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].dns_name : null
  description = "EFS DNS name (if EFS is enabled)"
}

output "autoscaling_target_arn" {
  value       = var.enable_auto_scaling ? aws_appautoscaling_target.ecs_target[0].arn : null
  description = "Auto scaling target ARN (if auto scaling is enabled)"
}

output "autoscaling_cpu_policy_arn" {
  value       = var.enable_auto_scaling ? aws_appautoscaling_policy.ecs_policy_cpu[0].arn : null
  description = "Auto scaling CPU policy ARN (if auto scaling is enabled)"
}

output "autoscaling_memory_policy_arn" {
  value       = var.enable_auto_scaling ? aws_appautoscaling_policy.ecs_policy_memory[0].arn : null
  description = "Auto scaling memory policy ARN (if auto scaling is enabled)"
}
