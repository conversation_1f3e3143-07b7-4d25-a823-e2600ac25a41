
resource "aws_iam_role" "execution" {
  name               = "${var.name}-execution-role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json

  inline_policy {
    name   = "inline"
    policy = data.aws_iam_policy_document.permissions.json
  }
}

resource "aws_iam_role" "task" {
  name               = "${var.name}-task-role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "execution_attach" {
  for_each   = toset([
    "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
    "arn:aws:iam::aws:policy/CloudWatchLogsFullAccess"
  ])
  policy_arn = each.value
  role       = aws_iam_role.execution.name
}

resource "aws_iam_role_policy_attachment" "task_attach" {
  for_each   = toset([
    "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
    "arn:aws:iam::aws:policy/CloudWatchLogsFullAccess"
  ])
  policy_arn = each.value
  role       = aws_iam_role.task.name
}

data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      identifiers = [
        "ecs-tasks.amazonaws.com",
        "ecs.amazonaws.com"
      ]
      type = "Service"
    }
  }
}

data "aws_iam_policy_document" "permissions" {
  statement {
    actions   = ["logs:*"]
    effect    = "Allow"
    resources = ["*"]
  }
}

# EFS IAM permissions (conditional)
data "aws_iam_policy_document" "efs_permissions" {
  count = var.enable_efs ? 1 : 0

  statement {
    actions = [
      "elasticfilesystem:ClientMount",
      "elasticfilesystem:ClientWrite",
      "elasticfilesystem:ClientRootAccess",
      "elasticfilesystem:DescribeFileSystems",
      "elasticfilesystem:DescribeAccessPoints",
      "elasticfilesystem:DescribeMountTargets"
    ]
    effect    = "Allow"
    resources = [aws_efs_file_system.efs[0].arn]
  }
}

resource "aws_iam_role_policy" "efs_policy" {
  count  = var.enable_efs ? 1 : 0
  name   = "${var.name}-efs-policy"
  role   = aws_iam_role.task.id
  policy = data.aws_iam_policy_document.efs_permissions[0].json
}

resource "aws_cloudwatch_log_group" "log" {
  name              = var.name
  retention_in_days = var.log_retention_days
}

resource "aws_lb_target_group" "tg" {
  name        = var.name
  vpc_id      = var.vpc_id
  port        = var.application_port
  protocol    = "HTTP"
  target_type = "ip"

  health_check {
    enabled             = true
    interval            = 300
    healthy_threshold   = 3
    protocol            = "HTTP"
    path                =  var.health_check_url
    port                = "${var.application_port}"
    timeout             = 60
    unhealthy_threshold = 3
  }
}

resource "aws_lb_listener_rule" "rule" {
  listener_arn = var.alb_listener_arn
  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.tg.arn
  }
  condition {
    host_header {
      values = ["${var.name}.${var.envzone_name}"]
    }
  }
}

resource "aws_ecs_task_definition" "td" {
  family                   = var.name
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = var.cpu
  memory                   = var.memory
  task_role_arn            = aws_iam_role.task.arn
  execution_role_arn       = aws_iam_role.execution.arn

  # EFS Volume configuration (conditional)
  dynamic "volume" {
    for_each = var.enable_efs ? [1] : []
    content {
      name = "${var.name}-efs-volume"
      efs_volume_configuration {
        file_system_id = aws_efs_file_system.efs[0].id
        root_directory = "/"
        transit_encryption = "ENABLED"
        authorization_config {
          access_point_id = aws_efs_access_point.reports_ap[0].id
          iam             = "ENABLED"
        }
      }
    }
  }

  container_definitions = jsonencode([
    {
      name  = "app",
      image = var.image,
      portMappings = [{
        containerPort = var.application_port
      }]

      # EFS mount points (conditional)
      mountPoints = var.enable_efs ? [{
        sourceVolume  = "${var.name}-efs-volume"
        containerPath = var.efs_mount_path
        readOnly      = false
      }] : [],
      user = "1000:1000"
    }
  ])
}

resource "aws_security_group" "secgroup" {
  name_prefix = var.name
  vpc_id      = var.vpc_id

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
  }

  ingress {
    cidr_blocks = [var.private_cidr_block]
    from_port   = var.application_port
    to_port     = var.application_port
    protocol    = "tcp"
  }
}

resource "aws_efs_access_point" "reports_ap" {
  count          = var.enable_efs ? 1 : 0
  file_system_id = aws_efs_file_system.efs[0].id
    posix_user {
      uid = 1000
      gid = 1000
    }

    root_directory {
      path = "/reports"
      creation_info {
        owner_uid = 1000
        owner_gid = 1000
        permissions = "0777"
      }
    }
  }

resource "aws_ecs_service" "svc" {
  name                    = var.name
  cluster                 = var.cluster_name
  task_definition         = aws_ecs_task_definition.td.arn
  enable_ecs_managed_tags = true
  desired_count           = 0
  launch_type             = "FARGATE"

  network_configuration {
    assign_public_ip = false
    security_groups  = [aws_security_group.secgroup.id]
    subnets          = var.subnet_ids
  }

  load_balancer {
    container_name   = "app"
    container_port   = var.application_port
    target_group_arn = aws_lb_target_group.tg.arn
  }

  depends_on = [aws_ecs_task_definition.td]
  lifecycle {
    ignore_changes = [ desired_count,task_definition ]
  }
}

# EFS Resources (conditional)
resource "aws_efs_file_system" "efs" {
  count            = var.enable_efs ? 1 : 0
  performance_mode = var.efs_performance_mode
  throughput_mode  = var.efs_throughput_mode
  encrypted        = true

  tags = {
    Name = "${var.name}-efs"
  }
}

resource "aws_security_group" "efs" {
  count       = var.enable_efs ? 1 : 0
  name_prefix = "${var.name}-efs-"
  vpc_id      = var.vpc_id

  ingress {
    from_port       = 2049
    to_port         = 2049
    protocol        = "tcp"
    security_groups = [aws_security_group.secgroup.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.name}-efs-sg"
  }
}

resource "aws_efs_mount_target" "efs_mount" {
  count           = var.enable_efs ? length(var.subnet_ids) : 0
  file_system_id  = aws_efs_file_system.efs[0].id
  subnet_id       = var.subnet_ids[count.index]
  security_groups = [aws_security_group.efs[0].id]
}

resource "aws_route53_record" "api_dns" {
  name    = "${var.name}.${var.envzone_name}"
  type    = "CNAME"
  zone_id = var.envzone_zone_id
  ttl     = 300
  records = [var.alb_dns_name]
}

# Auto Scaling Resources (conditional)
resource "aws_appautoscaling_target" "ecs_target" {
  count              = var.enable_auto_scaling ? 1 : 0
  max_capacity       = var.max_capacity
  min_capacity       = var.min_capacity
  resource_id        = "service/${var.cluster_name}/${aws_ecs_service.svc.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"

  depends_on = [aws_ecs_service.svc]
}

# Auto Scaling Policy - CPU
resource "aws_appautoscaling_policy" "ecs_policy_cpu" {
  count              = var.enable_auto_scaling ? 1 : 0
  name               = "${var.name}-cpu-scaling"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.ecs_target[0].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_target[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_target[0].service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = var.target_cpu_utilization
    scale_in_cooldown  = var.scale_down_cooldown
    scale_out_cooldown = var.scale_up_cooldown
  }
}

# Auto Scaling Policy - Memory
resource "aws_appautoscaling_policy" "ecs_policy_memory" {
  count              = var.enable_auto_scaling ? 1 : 0
  name               = "${var.name}-memory-scaling"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.ecs_target[0].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_target[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_target[0].service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value       = var.target_memory_utilization
    scale_in_cooldown  = var.scale_down_cooldown
    scale_out_cooldown = var.scale_up_cooldown
  }
}
